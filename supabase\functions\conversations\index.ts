import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // إنشاء عميل Supabase
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    const url = new URL(req.url)
    const method = req.method

    switch (method) {
      case 'GET':
        return await handleGetConversations(supabase, url)
      case 'POST':
        return await handleCreateConversation(supabase, req)
      case 'PUT':
        return await handleUpdateConversation(supabase, req)
      case 'DELETE':
        return await handleDeleteConversation(supabase, url)
      default:
        return new Response('Method not allowed', { status: 405, headers: corsHeaders })
    }

  } catch (error) {
    console.error('Error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    )
  }
})

// جلب المحادثات
async function handleGetConversations(supabase: any, url: URL) {
  const userId = url.searchParams.get('userId')
  const conversationId = url.searchParams.get('conversationId')

  if (!userId) {
    return new Response(
      JSON.stringify({ error: 'User ID is required' }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  if (conversationId) {
    // جلب محادثة واحدة
    const { data, error } = await supabase
      .from('conversations')
      .select('*')
      .eq('id', conversationId)
      .eq('user_id', userId)
      .single()

    if (error) {
      return new Response(
        JSON.stringify({ error: error.message }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify(data),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } else {
    // جلب جميع المحادثات للمستخدم
    const { data, error } = await supabase
      .from('conversations')
      .select('id, title, project_type, writing_style, created_at, updated_at')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })

    if (error) {
      return new Response(
        JSON.stringify({ error: error.message }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify(data || []),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}

// إنشاء محادثة جديدة
async function handleCreateConversation(supabase: any, req: Request) {
  const { userId, title, projectType, writingStyle } = await req.json()

  if (!userId || !projectType || !writingStyle) {
    return new Response(
      JSON.stringify({ error: 'Missing required fields' }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  const conversationId = crypto.randomUUID()
  const now = new Date().toISOString()

  const { data, error } = await supabase
    .from('conversations')
    .insert({
      id: conversationId,
      user_id: userId,
      title: title || `محادثة جديدة - ${new Date().toLocaleDateString('ar-SA')}`,
      project_type: projectType,
      writing_style: writingStyle,
      messages: [],
      created_at: now,
      updated_at: now
    })
    .select()
    .single()

  if (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify(data),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

// تحديث محادثة
async function handleUpdateConversation(supabase: any, req: Request) {
  const { conversationId, userId, title, messages } = await req.json()

  if (!conversationId || !userId) {
    return new Response(
      JSON.stringify({ error: 'Conversation ID and User ID are required' }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  const updateData: any = {
    updated_at: new Date().toISOString()
  }

  if (title) updateData.title = title
  if (messages) updateData.messages = messages

  const { data, error } = await supabase
    .from('conversations')
    .update(updateData)
    .eq('id', conversationId)
    .eq('user_id', userId)
    .select()
    .single()

  if (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify(data),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

// حذف محادثة
async function handleDeleteConversation(supabase: any, url: URL) {
  const conversationId = url.searchParams.get('conversationId')
  const userId = url.searchParams.get('userId')

  if (!conversationId || !userId) {
    return new Response(
      JSON.stringify({ error: 'Conversation ID and User ID are required' }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  const { error } = await supabase
    .from('conversations')
    .delete()
    .eq('id', conversationId)
    .eq('user_id', userId)

  if (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify({ success: true }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}
