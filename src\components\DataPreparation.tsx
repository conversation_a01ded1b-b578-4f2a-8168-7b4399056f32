
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { BookOpen, FileText, Filter, Database, CheckCircle } from "lucide-react";
import { useState } from "react";

export const DataPreparation = () => {
  const [processingStep, setProcessingStep] = useState(0);
  
  const dataSourcees = [
    {
      name: "أعمال نجيب محفوظ",
      count: "42 رواية وقصة",
      status: "مكتمل",
      icon: BookOpen
    },
    {
      name: "أعمال أحمد مراد",
      count: "15 رواية",
      status: "مكتمل", 
      icon: FileText
    },
    {
      name: "الشعر العربي الكلاسيكي",
      count: "5000+ قصيدة",
      status: "قيد المعالجة",
      icon: BookO<PERSON>
    },
    {
      name: "الأدب العربي المعاصر",
      count: "200+ عمل",
      status: "في الانتظار",
      icon: FileText
    }
  ];

  const processingSteps = [
    "جمع النصوص الأدبية",
    "تنظيف البيانات وإزالة التكرار",
    "معالجة التشكيل والحروف الخاصة",
    "تقسيم النصوص إلى فقرات متماسكة",
    "إنشاء فهارس ومعاجم المفردات",
    "تحويل البيانات إلى تنسيق التدريب"
  ];

  const handleStartProcessing = () => {
    // محاكاة معالجة البيانات
    const interval = setInterval(() => {
      setProcessingStep(prev => {
        if (prev >= processingSteps.length - 1) {
          clearInterval(interval);
          return prev;
        }
        return prev + 1;
      });
    }, 2000);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 mb-2">إعداد ومعالجة البيانات التدريبية</h2>
        <p className="text-slate-600">
          تجميع وتنظيف قاعدة بيانات شاملة من الأدب العربي الكلاسيكي والمعاصر
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="w-5 h-5 text-blue-600" />
              مصادر البيانات
            </CardTitle>
            <CardDescription>قاعدة البيانات الأدبية المتنوعة</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {dataSourcees.map((source, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <source.icon className="w-5 h-5 text-blue-600" />
                  <div>
                    <div className="font-medium">{source.name}</div>
                    <div className="text-sm text-slate-600">{source.count}</div>
                  </div>
                </div>
                <Badge 
                  variant={source.status === 'مكتمل' ? 'default' : 'secondary'}
                  className={source.status === 'مكتمل' ? 'bg-emerald-100 text-emerald-700' : ''}
                >
                  {source.status}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="w-5 h-5 text-purple-600" />
              مراحل المعالجة
            </CardTitle>
            <CardDescription>خطوات تنظيف وإعداد البيانات</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {processingSteps.map((step, index) => (
              <div key={index} className="flex items-center gap-3">
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-sm ${
                  index <= processingStep 
                    ? 'bg-emerald-500 text-white' 
                    : 'bg-slate-200 text-slate-600'
                }`}>
                  {index <= processingStep ? (
                    <CheckCircle className="w-4 h-4" />
                  ) : (
                    index + 1
                  )}
                </div>
                <span className={index <= processingStep ? 'text-emerald-700' : 'text-slate-600'}>
                  {step}
                </span>
              </div>
            ))}
            
            <Button 
              className="w-full mt-4" 
              onClick={handleStartProcessing}
              disabled={processingStep > 0}
            >
              {processingStep === 0 ? 'بدء معالجة البيانات' : 'جاري المعالجة...'}
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>إحصائيات قاعدة البيانات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-700">2.5M</div>
              <div className="text-sm text-blue-600">كلمة</div>
            </div>
            <div className="text-center p-4 bg-emerald-50 rounded-lg">
              <div className="text-2xl font-bold text-emerald-700">450K</div>
              <div className="text-sm text-emerald-600">جملة</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-700">25K</div>
              <div className="text-sm text-purple-600">فقرة</div>
            </div>
            <div className="text-center p-4 bg-amber-50 rounded-lg">
              <div className="text-2xl font-bold text-amber-700">85%</div>
              <div className="text-sm text-amber-600">جودة البيانات</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
