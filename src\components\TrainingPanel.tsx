import { Card, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Play, Pause, Square, Settings, TrendingUp } from "lucide-react";
import { useState, useEffect } from "react";

interface TrainingPanelProps {
  progress: number;
  setProgress: (progress: number) => void;
}

export const TrainingPanel = ({ progress, setProgress }: TrainingPanelProps) => {
  const [isTraining, setIsTraining] = useState(false);
  const [epoch, setEpoch] = useState(1);
  const [loss, setLoss] = useState(2.45);

  const trainingConfig = {
    batchSize: 32,
    learningRate: 0.0001,
    epochs: 50,
    warmupSteps: 1000,
    maxLength: 2048
  };

  const handleStartTraining = () => {
    setIsTraining(true);
    const interval = setInterval(() => {
      const newProgress = progress + Math.random() * 5;
      if (newProgress >= 100) {
        setIsTraining(false);
        clearInterval(interval);
        setProgress(100);
        return;
      }
      
      // محاكاة تحديث المقاييس
      setEpoch(Math.floor(newProgress / 2) + 1);
      setLoss(2.45 - (newProgress / 100) * 1.8);
      
      setProgress(newProgress);
    }, 1000);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 mb-2">تدريب النموذج الأدبي</h2>
        <p className="text-slate-600">
          ضبط دقيق (Fine-tuning) للنموذج على البيانات الأدبية العربية
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-green-600" />
              تقدم التدريب
            </CardTitle>
            <CardDescription>
              العصر {epoch} من {trainingConfig.epochs} • Loss: {loss.toFixed(3)}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">التقدم الإجمالي</span>
                <span className="text-sm text-slate-600">{progress.toFixed(1)}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>

            <div className="grid grid-cols-3 gap-4 mt-6">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-lg font-bold text-blue-700">{epoch}</div>
                <div className="text-sm text-blue-600">العصر الحالي</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-lg font-bold text-green-700">{loss.toFixed(3)}</div>
                <div className="text-sm text-green-600">معدل الفقدان</div>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <div className="text-lg font-bold text-purple-700">
                  {isTraining ? '12h 34m' : 'متوقف'}
                </div>
                <div className="text-sm text-purple-600">الوقت المتبقي</div>
              </div>
            </div>

            <div className="flex gap-2 mt-6">
              <Button 
                onClick={handleStartTraining}
                disabled={isTraining || progress >= 100}
                className="flex-1"
              >
                <Play className="w-4 h-4 mr-2" />
                {isTraining ? 'جاري التدريب...' : 'بدء التدريب'}
              </Button>
              <Button variant="outline" disabled={!isTraining}>
                <Pause className="w-4 h-4" />
              </Button>
              <Button variant="outline" disabled={!isTraining}>
                <Square className="w-4 h-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5 text-orange-600" />
              إعدادات التدريب
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm">حجم الدفعة</span>
                <Badge variant="secondary">{trainingConfig.batchSize}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">معدل التعلم</span>
                <Badge variant="secondary">{trainingConfig.learningRate}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">عدد العصور</span>
                <Badge variant="secondary">{trainingConfig.epochs}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">خطوات الإحماء</span>
                <Badge variant="secondary">{trainingConfig.warmupSteps}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">الحد الأقصى للطول</span>
                <Badge variant="secondary">{trainingConfig.maxLength}</Badge>
              </div>
            </div>

            <Button variant="outline" className="w-full mt-4">
              تعديل الإعدادات
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>سجل التدريب</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-40 overflow-y-auto font-mono text-sm bg-slate-900 text-green-400 p-4 rounded-lg">
            <div>[2024-01-15 14:30:25] بدء تحميل البيانات التدريبية...</div>
            <div>[2024-01-15 14:30:26] تم تحميل 450,000 عينة تدريبية</div>
            <div>[2024-01-15 14:30:27] تهيئة النموذج Llama 3.1 70B...</div>
            <div>[2024-01-15 14:30:28] بدء العصر الأول...</div>
            {isTraining && (
              <>
                <div>[2024-01-15 14:30:29] الدفعة 1/1000 - Loss: {loss.toFixed(3)}</div>
                <div>[2024-01-15 14:30:30] معدل التقدم: {progress.toFixed(1)}%</div>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
