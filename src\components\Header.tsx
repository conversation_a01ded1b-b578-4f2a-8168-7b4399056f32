
import { <PERSON>, <PERSON>O<PERSON>, Sparkles } from "lucide-react";

export const Header = () => {
  return (
    <header className="bg-white/90 backdrop-blur-sm border-b border-amber-200 sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <Brain className="w-8 h-8 text-emerald-600" />
              <Sparkles className="w-4 h-4 text-amber-500 absolute -top-1 -right-1" />
            </div>
            <h1 className="text-2xl font-bold text-slate-800">الأديب الذكي</h1>
          </div>
          
          <nav className="flex items-center gap-6">
            <div className="flex items-center gap-2 text-slate-600">
              <BookOpen className="w-5 h-5" />
              <span className="font-medium">مكتبة الأدب العربي</span>
            </div>
            <div className="px-4 py-2 bg-emerald-100 text-emerald-700 rounded-full text-sm font-medium">
              جاهز للاستخدام
            </div>
          </nav>
        </div>
      </div>
    </header>
  );
};
