
import { useState, useRef, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MessageCircle, Send, BookOpen, User, Bot, Copy, Download, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { createClient } from '@supabase/supabase-js';

interface Message {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
}

interface ChatBotProps {
  activeModel: string;
}

const supabase = createClient(
  'https://xpbhzuzbnwpvlpgrcijf.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwYmh6dXpibnBwdmxwZ3JjaWpmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzI3MzE0OTksImV4cCI6MjA0ODMwNzQ5OX0.TgE7YZo7R5PkwmCzgxq1ssMxvGMT1KlZTNnmIQY7ZLI'
);

export const ChatBot = ({ activeModel }: ChatBotProps) => {
  const { toast } = useToast();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'bot',
      content: 'مرحباً بك! أنا مساعدك الذكي لكتابة الأدب العربي. يمكنني مساعدتك في بناء روايات وقصائد خطوة بخطوة. كيف يمكنني مساعدتك اليوم؟',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [projectType, setProjectType] = useState("رواية");
  const [writingStyle, setWritingStyle] = useState("نجيب محفوظ");
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const projectTypes = ["رواية", "قصة قصيرة", "شعر", "مقالة أدبية"];
  const writingStyles = ["نجيب محفوظ", "أحمد مراد", "غسان كنفاني", "أحلام مستغانمي", "طه حسين"];

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage("");
    setIsTyping(true);

    try {
      console.log('Sending message to AI:', inputMessage);
      
      const { data, error } = await supabase.functions.invoke('ai-chat', {
        body: {
          message: inputMessage,
          projectType,
          writingStyle,
          conversationHistory: messages
        }
      });

      if (error) {
        console.error('Supabase function error:', error);
        throw error;
      }

      console.log('AI response received:', data);

      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: data.response,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botMessage]);
      
    } catch (error) {
      console.error('Error calling AI:', error);
      toast({
        title: "خطأ في الاتصال",
        description: "حدث خطأ أثناء التواصل مع الذكاء الاصطناعي. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      });
      
      // رسالة خطأ للمستخدم
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'bot',
        content: 'عذراً، حدث خطأ في التواصل مع النموذج. يرجى المحاولة مرة أخرى.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  const exportChat = () => {
    const chatContent = messages.map(msg => 
      `${msg.type === 'user' ? 'أنت' : 'المساعد'}: ${msg.content}`
    ).join('\n\n');
    
    const blob = new Blob([chatContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `حوار-${projectType}-${new Date().toLocaleDateString('ar')}.txt`;
    a.click();
  };

  const copyLastResponse = () => {
    const lastBotMessage = messages.filter(msg => msg.type === 'bot').pop();
    if (lastBotMessage) {
      navigator.clipboard.writeText(lastBotMessage.content);
      toast({
        title: "تم النسخ",
        description: "تم نسخ آخر رد إلى الحافظة",
      });
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 mb-2">بوت الدردشة الأدبي</h2>
        <p className="text-slate-600">احترف كتابة الأدب العربي من خلال حوار تفاعلي مع الذكاء الاصطناعي</p>
      </div>

      <div className="grid md:grid-cols-4 gap-6">
        <Card className="md:col-span-3">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="w-5 h-5 text-blue-600" />
              محادثة إبداعية
            </CardTitle>
            <div className="flex gap-2">
              <Badge variant="outline">{activeModel}</Badge>
              <Badge variant="secondary">{projectType}</Badge>
              <Badge variant="outline">{writingStyle}</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-96 mb-4 p-4 border rounded-lg bg-slate-50" ref={scrollAreaRef}>
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`flex gap-2 max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse' : ''}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        message.type === 'user' ? 'bg-emerald-100' : 'bg-blue-100'
                      }`}>
                        {message.type === 'user' ? (
                          <User className="w-4 h-4 text-emerald-600" />
                        ) : (
                          <Bot className="w-4 h-4 text-blue-600" />
                        )}
                      </div>
                      <div className={`p-3 rounded-lg ${
                        message.type === 'user' 
                          ? 'bg-emerald-600 text-white' 
                          : 'bg-white border border-slate-200'
                      }`}>
                        <p className="text-sm leading-relaxed">{message.content}</p>
                        <span className="text-xs opacity-70 mt-1 block">
                          {message.timestamp.toLocaleTimeString('ar')}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
                
                {isTyping && (
                  <div className="flex gap-3 justify-start">
                    <div className="flex gap-2">
                      <div className="w-8 h-8 rounded-full flex items-center justify-center bg-blue-100">
                        <Bot className="w-4 h-4 text-blue-600" />
                      </div>
                      <div className="p-3 rounded-lg bg-white border border-slate-200">
                        <div className="flex gap-1">
                          <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>

            <div className="flex gap-2">
              <Input
                placeholder="اكتب رسالتك هنا..."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && !isTyping && handleSendMessage()}
                className="flex-1"
                disabled={isTyping}
              />
              <Button onClick={handleSendMessage} disabled={!inputMessage.trim() || isTyping}>
                {isTyping ? <Loader2 className="w-4 h-4 animate-spin" /> : <Send className="w-4 h-4" />}
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">إعدادات المحادثة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">نوع المشروع</label>
              <Select value={projectType} onValueChange={setProjectType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {projectTypes.map((type) => (
                    <SelectItem key={type} value={type}>{type}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">أسلوب الكتابة</label>
              <Select value={writingStyle} onValueChange={setWritingStyle}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {writingStyles.map((style) => (
                    <SelectItem key={style} value={style}>{style}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="pt-4 space-y-2">
              <Button variant="outline" size="sm" className="w-full" onClick={exportChat}>
                <Download className="w-4 h-4 mr-2" />
                تصدير المحادثة
              </Button>
              <Button variant="ghost" size="sm" className="w-full" onClick={copyLastResponse}>
                <Copy className="w-4 h-4 mr-2" />
                نسخ آخر رد
              </Button>
            </div>

            <div className="pt-4 border-t">
              <h4 className="font-medium text-sm mb-2">اقتراحات للبدء:</h4>
              <div className="space-y-1">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full text-xs h-auto p-2 text-right justify-start"
                  onClick={() => setInputMessage("أريد كتابة رواية عن شاب في القاهرة")}
                >
                  رواية عن الحياة في القاهرة
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full text-xs h-auto p-2 text-right justify-start"
                  onClick={() => setInputMessage("ساعدني في كتابة قصيدة عن الوطن")}
                >
                  قصيدة عن حب الوطن
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full text-xs h-auto p-2 text-right justify-start"
                  onClick={() => setInputMessage("كيف أطور شخصيات روايتي؟")}
                >
                  تطوير شخصيات الرواية
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
