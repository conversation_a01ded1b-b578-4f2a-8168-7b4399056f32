
import { Card, CardContent, CardDes<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { PenTool, Sparkles, BookOpen, Copy, Download } from "lucide-react";
import { useState } from "react";

interface WritingInterfaceProps {
  activeModel: string;
}

export const WritingInterface = ({ activeModel }: WritingInterfaceProps) => {
  const [prompt, setPrompt] = useState("");
  const [generatedText, setGeneratedText] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [writingStyle, setWritingStyle] = useState("نجيب محفوظ");
  const [textLength, setTextLength] = useState([500]);
  const [genre, setGenre] = useState("رواية");

  const writingStyles = [
    "نجيب محفوظ",
    "أحمد مراد", 
    "غسان كنفاني",
    "أحلام مستغانمي",
    "طه حسين",
    "أسلوب كلاسيكي",
    "أسلوب معاصر"
  ];

  const genres = [
    "رواية",
    "قصة قصيرة",
    "شعر",
    "مقالة أدبية",
    "نص تاريخي",
    "حوار فلسفي"
  ];

  const samplePrompts = [
    "اكتب فصلاً من رواية تحكي عن شاب يعيش في القاهرة ويواجه تحديات الحياة الحديثة",
    "اكتب قصيدة عن جمال الطبيعة في الربيع باللغة العربية الفصحى",
    "اكتب مونولوجاً داخلياً لشخصية تتذكر طفولتها في قرية صغيرة",
    "اكتب حواراً بين جيلين مختلفين حول التقاليد والحداثة"
  ];

  const handleGenerate = async () => {
    if (!prompt.trim()) return;
    
    setIsGenerating(true);
    setGeneratedText("");
    
    // محاكاة توليد النص
    const sampleTexts = {
      "نجيب محفوظ": `كانت القاهرة في ذلك الصباح تلبس حلة جديدة من الضوء الذهبي، والشوارع تموج بحركة الناس المتجهين إلى أعمالهم. وقف أحمد أمام النافذة يتأمل هذا المشهد اليومي المألوف، وفي قلبه شعور غامض بأن شيئاً ما سيتغير في حياته اليوم. كان قد أمضى سنوات طويلة في هذا الحي، يعرف كل زقاق وكل وجه، ولكن الحياة كانت تبدو له أحياناً كأنها كتاب مكتوب بلغة لا يفهمها تماماً.`,
      
      "أحمد مراد": `الليل يخيم على المدينة بثقله المعتاد، والأضواء الباهتة تكافح لتخترق الظلام. في شقة صغيرة بالدور السابع، يجلس كريم أمام شاشة الكمبيوتر محاولاً أن يجد إجابة للسؤال الذي يؤرقه منذ أسابيع. الأصوات في الشارع تختلط بأفكاره المشوشة، وكل رنة هاتف تجعل قلبه يخفق بقوة. كان يعلم أن الحقيقة قريبة، ولكنها مؤلمة أكثر مما تخيل.`
    };

    // محاكاة التأخير
    setTimeout(() => {
      const styleKey = writingStyle === "نجيب محفوظ" ? "نجيب محفوظ" : "أحمد مراد";
      setGeneratedText(sampleTexts[styleKey]);
      setIsGenerating(false);
    }, 3000);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 mb-2">واجهة الكتابة الإبداعية</h2>
        <p className="text-slate-600">
          استخدم قوة الذكاء الاصطناعي لإنتاج نصوص أدبية إبداعية بأساليب مختلفة
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PenTool className="w-5 h-5 text-blue-600" />
              منطقة الكتابة
            </CardTitle>
            <CardDescription>
              النموذج النشط: <Badge variant="outline">{activeModel}</Badge>
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">اكتب موضوعك أو فكرتك</label>
              <Textarea
                placeholder="اكتب هنا الموضوع الذي تريد تطويره إلى نص أدبي..."
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="min-h-[120px]"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <Button 
                onClick={handleGenerate}
                disabled={!prompt.trim() || isGenerating}
                className="bg-emerald-600 hover:bg-emerald-700"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                {isGenerating ? 'جاري الإنتاج...' : 'إنتاج النص'}
              </Button>
              <Button variant="outline">
                <BookOpen className="w-4 h-4 mr-2" />
                أمثلة للإلهام
              </Button>
            </div>

            {generatedText && (
              <div className="mt-6">
                <div className="flex justify-between items-center mb-2">
                  <label className="block text-sm font-medium">النص المُولد</label>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      <Copy className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg leading-relaxed text-justify">
                  {generatedText}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>إعدادات الكتابة</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">نوع الأدب</label>
              <Select value={genre} onValueChange={setGenre}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {genres.map((g) => (
                    <SelectItem key={g} value={g}>{g}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">أسلوب الكتابة</label>
              <Select value={writingStyle} onValueChange={setWritingStyle}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {writingStyles.map((style) => (
                    <SelectItem key={style} value={style}>{style}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                طول النص: {textLength[0]} كلمة
              </label>
              <Slider
                value={textLength}
                onValueChange={setTextLength}
                max={2000}
                min={100}
                step={50}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium">أمثلة للإلهام</label>
              {samplePrompts.map((example, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  size="sm"
                  className="w-full text-right justify-start text-xs h-auto p-2"
                  onClick={() => setPrompt(example)}
                >
                  {example.substring(0, 60)}...
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
