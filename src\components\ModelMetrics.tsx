
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON>Chart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from "recharts";
import { TrendingUp, Target, Zap, Award } from "lucide-react";

export const ModelMetrics = () => {
  const performanceData = [
    { epoch: 1, bleu: 0.25, perplexity: 45.2, loss: 2.1 },
    { epoch: 5, bleu: 0.32, perplexity: 38.7, loss: 1.8 },
    { epoch: 10, bleu: 0.41, perplexity: 32.1, loss: 1.5 },
    { epoch: 15, bleu: 0.48, perplexity: 28.3, loss: 1.3 },
    { epoch: 20, bleu: 0.53, perplexity: 25.6, loss: 1.1 },
    { epoch: 25, bleu: 0.58, perplexity: 23.4, loss: 0.9 },
  ];

  const qualityMetrics = [
    { metric: "إبداعية المحتوى", score: 87 },
    { metric: "سلامة اللغة", score: 94 },
    { metric: "تماسك السرد", score: 82 },
    { metric: "ثراء المفردات", score: 89 },
  ];

  const benchmarkResults = [
    {
      test: "نصوص نجيب محفوظ",
      bleu: 0.68,
      similarity: 0.84,
      quality: "ممتاز"
    },
    {
      test: "الشعر العربي",
      bleu: 0.59,
      similarity: 0.79,
      quality: "جيد جداً"
    },
    {
      test: "النثر المعاصر", 
      bleu: 0.72,
      similarity: 0.87,
      quality: "ممتاز"
    },
    {
      test: "النصوص التاريخية",
      bleu: 0.54,
      similarity: 0.73,
      quality: "جيد"
    }
  ];

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-slate-800 mb-2">تقييم ومقاييس الأداء</h2>
        <p className="text-slate-600">
          مراقبة وتحليل جودة النموذج باستخدام مقاييس متقدمة ومعايير أدبية
        </p>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="flex items-center justify-center p-6">
            <div className="text-center">
              <TrendingUp className="w-8 h-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-700">0.58</div>
              <div className="text-sm text-slate-600">BLEU Score</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center justify-center p-6">
            <div className="text-center">
              <Target className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-700">23.4</div>
              <div className="text-sm text-slate-600">Perplexity</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center justify-center p-6">
            <div className="text-center">
              <Zap className="w-8 h-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-purple-700">0.9</div>
              <div className="text-sm text-slate-600">Training Loss</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center justify-center p-6">
            <div className="text-center">
              <Award className="w-8 h-8 text-amber-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-amber-700">88%</div>
              <div className="text-sm text-slate-600">جودة عامة</div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>تطور الأداء عبر العصور</CardTitle>
            <CardDescription>تحسن مقاييس الجودة أثناء التدريب</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={performanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="epoch" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="bleu" stroke="#2563eb" strokeWidth={2} name="BLEU Score" />
                <Line type="monotone" dataKey="loss" stroke="#dc2626" strokeWidth={2} name="Loss" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>مقاييس الجودة الأدبية</CardTitle>
            <CardDescription>تقييم جوانب مختلفة من النص المُولد</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {qualityMetrics.map((metric, index) => (
              <div key={index} className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm font-medium">{metric.metric}</span>
                  <span className="text-sm text-slate-600">{metric.score}%</span>
                </div>
                <Progress value={metric.score} className="w-full" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>نتائج الاختبارات المرجعية</CardTitle>
          <CardDescription>مقارنة الأداء مع نصوص أدبية معيارية</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              {benchmarkResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div>
                    <div className="font-medium">{result.test}</div>
                    <div className="text-sm text-slate-600">
                      BLEU: {result.bleu} • تشابه: {result.similarity}
                    </div>
                  </div>
                  <Badge 
                    variant="secondary" 
                    className={
                      result.quality === 'ممتاز' ? 'bg-emerald-100 text-emerald-700' :
                      result.quality === 'جيد جداً' ? 'bg-blue-100 text-blue-700' :
                      'bg-amber-100 text-amber-700'
                    }
                  >
                    {result.quality}
                  </Badge>
                </div>
              ))}
            </div>
            
            <ResponsiveContainer width="100%" height={200}>
              <BarChart data={benchmarkResults}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="test" tick={{ fontSize: 10 }} />
                <YAxis />
                <Tooltip />
                <Bar dataKey="bleu" fill="#3b82f6" name="BLEU Score" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
